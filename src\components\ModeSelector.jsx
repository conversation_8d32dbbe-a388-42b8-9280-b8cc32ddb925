import { useTimer, TIMER_MODES } from "../contexts/TimerContext";
import { useTheme } from "../contexts/ThemeContext";
import Button from "./Button";

const ModeSelector = () => {
  const { mode, dispatch, actions, isRunning } = useTimer();
  const { theme } = useTheme();

  const handleModeChange = (newMode) => {
    if (isRunning) return; // Don't allow mode change while running
    dispatch({ type: actions.SET_MODE, payload: newMode });
  };

  const modes = [
    { key: TIMER_MODES.STOPWATCH, label: "Stopwatch", icon: "⏱️" },
    { key: TIMER_MODES.COUNTDOWN, label: "Countdown", icon: "⏰" },
    { key: TIMER_MODES.POMODORO, label: "Pomodoro", icon: "🍅" },
  ];

  return (
    <div className="flex flex-wrap gap-2 mb-4 justify-center">
      {modes.map(({ key, label, icon }) => (
        <Button
          key={key}
          onClick={() => handleModeChange(key)}
          disabled={isRunning}
          className={`
            px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
            ${
              mode === key
                ? `${theme.colors.primary} text-white shadow-md`
                : `${theme.colors.secondary} ${theme.colors.text}`
            }
            ${isRunning ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
          `}
          ariaLabel={`Switch to ${label} mode`}
        >
          <span>{icon}</span>
          <span className="text-sm font-medium">{label}</span>
        </Button>
      ))}
    </div>
  );
};

export default ModeSelector;
