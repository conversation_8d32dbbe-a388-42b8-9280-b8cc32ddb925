import { useTimer } from '../contexts/TimerContext';
import { formatTimeDisplay } from '../utils/timeUtils';
import Button from './Button';

const LapTimes = () => {
  const { lapTimes, isRunning, dispatch, actions } = useTimer();

  const handleAddLap = () => {
    if (!isRunning) return;
    dispatch({ type: actions.ADD_LAP_TIME });
  };

  const handleClearLaps = () => {
    dispatch({ type: actions.CLEAR_LAP_TIMES });
  };

  if (lapTimes.length === 0 && !isRunning) {
    return null;
  }

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-700">Lap Times</h3>
        <div className="flex gap-2">
          {isRunning && (
            <Button
              onClick={handleAddLap}
              className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              ariaLabel="Add lap time"
            >
              📍 Lap
            </Button>
          )}
          {lapTimes.length > 0 && (
            <Button
              onClick={handleClearLaps}
              className="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
              ariaLabel="Clear lap times"
            >
              🗑️ Clear
            </Button>
          )}
        </div>
      </div>

      {lapTimes.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto">
          <div className="space-y-2">
            {lapTimes.map((lap, index) => (
              <div
                key={lap.timestamp}
                className="flex justify-between items-center py-1 px-2 bg-white rounded text-sm"
              >
                <span className="font-medium text-gray-600">
                  Lap {lapTimes.length - index}
                </span>
                <span className="font-mono text-blue-600">
                  {formatTimeDisplay(lap.time)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LapTimes;
