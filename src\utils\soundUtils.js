/**
 * Sound utility functions for timer notifications
 */

// Default sound frequencies and patterns
export const SOUND_TYPES = {
  BEEP: 'beep',
  CHIME: 'chime',
  BELL: 'bell',
  TICK: 'tick',
  CUSTOM: 'custom'
};

// Audio context for generating sounds
let audioContext = null;

// Initialize audio context
const getAudioContext = () => {
  if (!audioContext) {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  }
  return audioContext;
};

/**
 * Play a beep sound with specified frequency and duration
 * @param {number} frequency - Frequency in Hz (default: 800)
 * @param {number} duration - Duration in milliseconds (default: 200)
 * @param {number} volume - Volume (0-1, default: 0.3)
 */
export const playBeep = (frequency = 800, duration = 200, volume = 0.3) => {
  try {
    const ctx = getAudioContext();
    const oscillator = ctx.createOscillator();
    const gainNode = ctx.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(ctx.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0, ctx.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume, ctx.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, ctx.currentTime + duration / 1000);

    oscillator.start(ctx.currentTime);
    oscillator.stop(ctx.currentTime + duration / 1000);
  } catch (error) {
    console.warn('Could not play beep sound:', error);
  }
};

/**
 * Play a chime sound (multiple frequencies)
 * @param {number} volume - Volume (0-1, default: 0.3)
 */
export const playChime = (volume = 0.3) => {
  const frequencies = [523.25, 659.25, 783.99]; // C5, E5, G5
  frequencies.forEach((freq, index) => {
    setTimeout(() => playBeep(freq, 300, volume), index * 100);
  });
};

/**
 * Play a bell sound
 * @param {number} volume - Volume (0-1, default: 0.3)
 */
export const playBell = (volume = 0.3) => {
  playBeep(800, 100, volume);
  setTimeout(() => playBeep(600, 200, volume * 0.7), 150);
};

/**
 * Play a tick sound (for timer ticking)
 * @param {number} volume - Volume (0-1, default: 0.1)
 */
export const playTick = (volume = 0.1) => {
  playBeep(1000, 50, volume);
};

/**
 * Play sound based on type
 * @param {string} type - Sound type from SOUND_TYPES
 * @param {number} volume - Volume (0-1, default: 0.3)
 */
export const playSound = (type = SOUND_TYPES.BEEP, volume = 0.3) => {
  switch (type) {
    case SOUND_TYPES.BEEP:
      playBeep(800, 200, volume);
      break;
    case SOUND_TYPES.CHIME:
      playChime(volume);
      break;
    case SOUND_TYPES.BELL:
      playBell(volume);
      break;
    case SOUND_TYPES.TICK:
      playTick(volume);
      break;
    default:
      playBeep(800, 200, volume);
  }
};

/**
 * Play custom audio file
 * @param {string} audioUrl - URL to audio file
 * @param {number} volume - Volume (0-1, default: 0.3)
 */
export const playCustomSound = (audioUrl, volume = 0.3) => {
  try {
    const audio = new Audio(audioUrl);
    audio.volume = volume;
    audio.play().catch(error => {
      console.warn('Could not play custom sound:', error);
    });
  } catch (error) {
    console.warn('Could not load custom sound:', error);
  }
};

/**
 * Test if audio is supported
 * @returns {boolean} True if audio is supported
 */
export const isAudioSupported = () => {
  return !!(window.AudioContext || window.webkitAudioContext);
};

/**
 * Request audio permission (for some browsers)
 */
export const requestAudioPermission = async () => {
  try {
    const ctx = getAudioContext();
    if (ctx.state === 'suspended') {
      await ctx.resume();
    }
    return true;
  } catch (error) {
    console.warn('Could not request audio permission:', error);
    return false;
  }
};
