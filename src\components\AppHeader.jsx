import { useTheme } from '../contexts/ThemeContext';
import ThemeSelector from './ThemeSelector';
import Settings from './Settings';
import Help from './Help';

const AppHeader = () => {
  const { theme } = useTheme();

  return (
    <header className={`${theme.colors.cardBg} rounded-2xl ${theme.colors.shadow} p-6 mb-6`}>
      <div className="flex items-center justify-between">
        {/* Logo and Title */}
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white text-2xl">⏱️</span>
          </div>
          <div>
            <h1 className={`text-2xl font-bold ${theme.colors.text} leading-tight`}>
              Advanced Timer
            </h1>
            <p className={`text-sm ${theme.colors.textSecondary}`}>
              Professional productivity timer
            </p>
          </div>
        </div>

        {/* Navigation Actions */}
        <div className="flex items-center gap-3">
          {/* Quick Stats */}
          <div className={`hidden md:flex items-center gap-4 px-4 py-2 rounded-lg ${theme.colors.secondary}`}>
            <div className="text-center">
              <div className={`text-lg font-bold ${theme.colors.text}`}>0</div>
              <div className={`text-xs ${theme.colors.textMuted}`}>Sessions</div>
            </div>
            <div className="w-px h-8 bg-gray-300"></div>
            <div className="text-center">
              <div className={`text-lg font-bold ${theme.colors.text}`}>0h</div>
              <div className={`text-xs ${theme.colors.textMuted}`}>Today</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            <Help />
            <Settings />
            <ThemeSelector />
          </div>
        </div>
      </div>

      {/* Quick Mode Indicators */}
      <div className="flex items-center justify-center gap-6 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
          <span>Stopwatch</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
          <span>Countdown</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-500">
          <span className="w-2 h-2 bg-red-500 rounded-full"></span>
          <span>Pomodoro</span>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
