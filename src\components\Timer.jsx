import { formatTimeDisplay } from "../utils/timeUtils";
import { useTimer, TIMER_MODES } from "../contexts/TimerContext";

const Timer = ({ timer, isRunning }) => {
  const { mode, initialTime } = useTimer();

  // Calculate progress for countdown mode
  const getProgress = () => {
    if (mode !== TIMER_MODES.COUNTDOWN || initialTime === 0) return 0;
    return ((initialTime - timer) / initialTime) * 100;
  };

  const getModeIcon = () => {
    switch (mode) {
      case TIMER_MODES.COUNTDOWN:
        return "⏰";
      case TIMER_MODES.POMODORO:
        return "🍅";
      default:
        return "⏱️";
    }
  };

  const progress = getProgress();

  return (
    <div className="text-center">
      {/* Mode indicator and icon */}
      <div className="mb-4">
        <span
          className={`text-3xl inline-block ${
            isRunning ? "animate-pulse" : ""
          }`}
        >
          {getModeIcon()}
        </span>
        <div className="text-sm text-gray-500 mt-1 capitalize">{mode} Mode</div>
      </div>

      {/* Progress bar for countdown mode */}
      {mode === TIMER_MODES.COUNTDOWN && initialTime > 0 && (
        <div className="mb-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-1000"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {Math.round(progress)}% complete
          </div>
        </div>
      )}

      {/* Main timer display */}
      <div className="font-mono text-6xl font-bold text-gray-800 mb-2">
        {formatTimeDisplay(timer)}
      </div>

      {/* Additional info for countdown mode */}
      {mode === TIMER_MODES.COUNTDOWN && initialTime > 0 && (
        <div className="text-sm text-gray-500">
          Started with {formatTimeDisplay(initialTime)}
        </div>
      )}
    </div>
  );
};

export default Timer;
