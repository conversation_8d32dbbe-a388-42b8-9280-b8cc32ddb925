const Button = ({
  className = '',
  children,
  onClick,
  ref,
  disabled = false,
  type = 'button',
  ariaLabel
}) => {
  return (
    <button
      className={`${className} ${disabled ? 'disabled' : ''}`}
      onClick={onClick}
      ref={ref}
      disabled={disabled}
      type={type}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
    >
      {children}
    </button>
  );
};


export default Button;
