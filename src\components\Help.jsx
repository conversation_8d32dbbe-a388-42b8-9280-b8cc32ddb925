import { useState } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import Button from './Button';

const Help = () => {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const shortcuts = [
    { key: 'Space', description: 'Start/Pause timer' },
    { key: 'R', description: 'Reset timer' },
    { key: 'L', description: 'Add lap time (while running)' },
    { key: 'F', description: 'Toggle fullscreen' },
    { key: 'S', description: 'Open settings' },
    { key: 'T', description: 'Cycle through themes' },
    { key: '1', description: 'Switch to Stopwatch mode' },
    { key: '2', description: 'Switch to Countdown mode' },
    { key: '3', description: 'Switch to Pomodoro mode' },
    { key: '↑', description: 'Increase time (when stopped)' },
    { key: '↓', description: 'Decrease time (when stopped)' },
    { key: 'Ctrl+S', description: 'Export timer data' },
    { key: 'Ctrl+O', description: 'Import timer data' },
    { key: 'Esc', description: 'Close modals/Exit fullscreen' },
    { key: '?', description: 'Show this help' }
  ];

  const features = [
    {
      title: 'Timer Modes',
      items: [
        'Stopwatch: Count up from zero',
        'Countdown: Count down from set time',
        'Pomodoro: Work/break cycles (coming soon)'
      ]
    },
    {
      title: 'Time Input',
      items: [
        'Click "Set Time" to manually enter hours, minutes, seconds',
        'Use preset buttons for quick time setting',
        'Time persists between sessions'
      ]
    },
    {
      title: 'Lap Times',
      items: [
        'Press "Lap" button or L key while timer is running',
        'View all lap times in chronological order',
        'Clear all lap times with the Clear button'
      ]
    },
    {
      title: 'Themes & Settings',
      items: [
        'Choose from 6 beautiful themes',
        'Enable/disable sound alerts and notifications',
        'Customize tick sounds and alert types',
        'Settings are saved automatically'
      ]
    }
  ];

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={`
          px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
          ${theme.colors.secondary} ${theme.colors.text}
        `}
        ariaLabel="Show help"
      >
        ❓ Help
      </Button>
    );
  }

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-20" 
        onClick={() => setIsOpen(false)}
      />
      
      {/* Help Modal */}
      <div className={`
        fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30
        w-full max-w-2xl mx-4 p-6 rounded-xl max-h-[90vh] overflow-y-auto
        ${theme.colors.cardBg} ${theme.colors.shadow}
      `}>
        <div className="flex justify-between items-center mb-6">
          <h2 className={`text-2xl font-bold ${theme.colors.text}`}>
            Help & Keyboard Shortcuts
          </h2>
          <Button
            onClick={() => setIsOpen(false)}
            className={`
              px-3 py-1 rounded-lg transition-colors
              ${theme.colors.secondary} ${theme.colors.text}
            `}
            ariaLabel="Close help"
          >
            ✕
          </Button>
        </div>

        <div className="space-y-8">
          {/* Keyboard Shortcuts */}
          <div>
            <h3 className={`text-xl font-semibold ${theme.colors.text} mb-4`}>
              ⌨️ Keyboard Shortcuts
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {shortcuts.map(({ key, description }) => (
                <div 
                  key={key}
                  className={`
                    flex items-center justify-between p-3 rounded-lg
                    ${theme.colors.secondary}
                  `}
                >
                  <span className={`${theme.colors.textSecondary}`}>
                    {description}
                  </span>
                  <kbd className={`
                    px-2 py-1 text-xs font-mono rounded border
                    ${theme.colors.cardBg} ${theme.colors.text} ${theme.colors.border}
                  `}>
                    {key}
                  </kbd>
                </div>
              ))}
            </div>
          </div>

          {/* Features Guide */}
          <div>
            <h3 className={`text-xl font-semibold ${theme.colors.text} mb-4`}>
              📚 Features Guide
            </h3>
            <div className="space-y-6">
              {features.map(({ title, items }) => (
                <div key={title}>
                  <h4 className={`text-lg font-medium ${theme.colors.text} mb-2`}>
                    {title}
                  </h4>
                  <ul className="space-y-1">
                    {items.map((item, index) => (
                      <li 
                        key={index}
                        className={`${theme.colors.textSecondary} flex items-start gap-2`}
                      >
                        <span className="text-blue-500 mt-1">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Tips */}
          <div className={`p-4 rounded-lg ${theme.colors.secondary}`}>
            <h4 className={`text-lg font-medium ${theme.colors.text} mb-2`}>
              💡 Pro Tips
            </h4>
            <ul className="space-y-1">
              <li className={`${theme.colors.textSecondary} flex items-start gap-2`}>
                <span className="text-yellow-500 mt-1">💡</span>
                <span>Use keyboard shortcuts for faster operation</span>
              </li>
              <li className={`${theme.colors.textSecondary} flex items-start gap-2`}>
                <span className="text-yellow-500 mt-1">💡</span>
                <span>Enable browser notifications to get alerts even when the tab is not active</span>
              </li>
              <li className={`${theme.colors.textSecondary} flex items-start gap-2`}>
                <span className="text-yellow-500 mt-1">💡</span>
                <span>Your settings and timer state are automatically saved</span>
              </li>
              <li className={`${theme.colors.textSecondary} flex items-start gap-2`}>
                <span className="text-yellow-500 mt-1">💡</span>
                <span>Try different themes to find your perfect look</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-4 border-t border-gray-200">
          <Button
            onClick={() => setIsOpen(false)}
            className={`
              w-full py-3 rounded-lg font-medium transition-colors
              ${theme.colors.primary} text-white
            `}
          >
            Got it!
          </Button>
        </div>
      </div>
    </>
  );
};

export default Help;
