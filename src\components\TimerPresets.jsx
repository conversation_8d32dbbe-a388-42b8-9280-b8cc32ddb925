import { useTimer, TIMER_MODES } from '../contexts/TimerContext';
import Button from './Button';

const TimerPresets = () => {
  const { mode, dispatch, actions, isRunning } = useTimer();

  const presets = [
    { label: '5 min', seconds: 300, icon: '⚡' },
    { label: '10 min', seconds: 600, icon: '⏰' },
    { label: '15 min', seconds: 900, icon: '📚' },
    { label: '25 min', seconds: 1500, icon: '🍅' }, // Pomodoro
    { label: '30 min', seconds: 1800, icon: '💪' },
    { label: '45 min', seconds: 2700, icon: '🎯' },
    { label: '1 hour', seconds: 3600, icon: '⏳' },
    { label: '2 hours', seconds: 7200, icon: '📖' }
  ];

  const handlePresetClick = (seconds) => {
    if (isRunning) return;
    
    if (mode === TIMER_MODES.COUNTDOWN) {
      dispatch({ type: actions.SET_INITIAL_TIME, payload: seconds });
    } else {
      dispatch({ type: actions.SET_TIMER, payload: seconds });
    }
  };

  // Only show presets for countdown mode or when timer is 0 in stopwatch mode
  if (mode === TIMER_MODES.STOPWATCH && isRunning) {
    return null;
  }

  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium text-gray-700 mb-2 text-center">
        Quick Presets
      </h3>
      <div className="grid grid-cols-4 gap-2">
        {presets.map(({ label, seconds, icon }) => (
          <Button
            key={seconds}
            onClick={() => handlePresetClick(seconds)}
            disabled={isRunning}
            className={`
              px-2 py-2 text-xs rounded-lg transition-all duration-200 flex flex-col items-center gap-1
              ${isRunning 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-blue-50 text-blue-700 hover:bg-blue-100 cursor-pointer'
              }
            `}
            ariaLabel={`Set timer to ${label}`}
          >
            <span className="text-sm">{icon}</span>
            <span className="font-medium">{label}</span>
          </Button>
        ))}
      </div>
    </div>
  );
};

export default TimerPresets;
