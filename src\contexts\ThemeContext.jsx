import { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  BLUE: 'blue',
  GREEN: 'green',
  PURPLE: 'purple',
  ORANGE: 'orange'
};

const themeConfig = {
  [THEMES.LIGHT]: {
    name: 'Light',
    icon: '☀️',
    colors: {
      bg: 'bg-gray-100',
      cardBg: 'bg-white',
      text: 'text-gray-800',
      textSecondary: 'text-gray-600',
      textMuted: 'text-gray-500',
      primary: 'bg-blue-500 hover:bg-blue-600',
      secondary: 'bg-gray-200 hover:bg-gray-300',
      success: 'bg-green-500 hover:bg-green-600',
      danger: 'bg-red-500 hover:bg-red-600',
      warning: 'bg-orange-500 hover:bg-orange-600',
      border: 'border-gray-200',
      shadow: 'shadow-lg'
    }
  },
  [THEMES.DARK]: {
    name: 'Dark',
    icon: '🌙',
    colors: {
      bg: 'bg-gray-900',
      cardBg: 'bg-gray-800',
      text: 'text-white',
      textSecondary: 'text-gray-300',
      textMuted: 'text-gray-400',
      primary: 'bg-blue-600 hover:bg-blue-700',
      secondary: 'bg-gray-700 hover:bg-gray-600',
      success: 'bg-green-600 hover:bg-green-700',
      danger: 'bg-red-600 hover:bg-red-700',
      warning: 'bg-orange-600 hover:bg-orange-700',
      border: 'border-gray-700',
      shadow: 'shadow-2xl'
    }
  },
  [THEMES.BLUE]: {
    name: 'Ocean',
    icon: '🌊',
    colors: {
      bg: 'bg-blue-50',
      cardBg: 'bg-blue-100',
      text: 'text-blue-900',
      textSecondary: 'text-blue-700',
      textMuted: 'text-blue-600',
      primary: 'bg-blue-600 hover:bg-blue-700',
      secondary: 'bg-blue-200 hover:bg-blue-300',
      success: 'bg-teal-500 hover:bg-teal-600',
      danger: 'bg-red-500 hover:bg-red-600',
      warning: 'bg-amber-500 hover:bg-amber-600',
      border: 'border-blue-200',
      shadow: 'shadow-lg shadow-blue-200/50'
    }
  },
  [THEMES.GREEN]: {
    name: 'Forest',
    icon: '🌲',
    colors: {
      bg: 'bg-green-50',
      cardBg: 'bg-green-100',
      text: 'text-green-900',
      textSecondary: 'text-green-700',
      textMuted: 'text-green-600',
      primary: 'bg-green-600 hover:bg-green-700',
      secondary: 'bg-green-200 hover:bg-green-300',
      success: 'bg-emerald-500 hover:bg-emerald-600',
      danger: 'bg-red-500 hover:bg-red-600',
      warning: 'bg-yellow-500 hover:bg-yellow-600',
      border: 'border-green-200',
      shadow: 'shadow-lg shadow-green-200/50'
    }
  },
  [THEMES.PURPLE]: {
    name: 'Cosmic',
    icon: '🌌',
    colors: {
      bg: 'bg-purple-50',
      cardBg: 'bg-purple-100',
      text: 'text-purple-900',
      textSecondary: 'text-purple-700',
      textMuted: 'text-purple-600',
      primary: 'bg-purple-600 hover:bg-purple-700',
      secondary: 'bg-purple-200 hover:bg-purple-300',
      success: 'bg-green-500 hover:bg-green-600',
      danger: 'bg-red-500 hover:bg-red-600',
      warning: 'bg-orange-500 hover:bg-orange-600',
      border: 'border-purple-200',
      shadow: 'shadow-lg shadow-purple-200/50'
    }
  },
  [THEMES.ORANGE]: {
    name: 'Sunset',
    icon: '🌅',
    colors: {
      bg: 'bg-orange-50',
      cardBg: 'bg-orange-100',
      text: 'text-orange-900',
      textSecondary: 'text-orange-700',
      textMuted: 'text-orange-600',
      primary: 'bg-orange-600 hover:bg-orange-700',
      secondary: 'bg-orange-200 hover:bg-orange-300',
      success: 'bg-green-500 hover:bg-green-600',
      danger: 'bg-red-500 hover:bg-red-600',
      warning: 'bg-yellow-500 hover:bg-yellow-600',
      border: 'border-orange-200',
      shadow: 'shadow-lg shadow-orange-200/50'
    }
  }
};

export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState(() => {
    const saved = localStorage.getItem('timerAppTheme');
    return saved && themeConfig[saved] ? saved : THEMES.LIGHT;
  });

  useEffect(() => {
    localStorage.setItem('timerAppTheme', currentTheme);
  }, [currentTheme]);

  const theme = themeConfig[currentTheme];

  const value = {
    currentTheme,
    theme,
    themes: themeConfig,
    setTheme: setCurrentTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
