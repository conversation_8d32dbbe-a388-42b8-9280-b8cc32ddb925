import { formatTimeDisplay } from "../utils/timeUtils";
import { useTimer, TIMER_MODES } from "../contexts/TimerContext";
import { useTheme } from "../contexts/ThemeContext";

const EnhancedTimer = ({ timer, isRunning }) => {
  const { mode, initialTime } = useTimer();
  const { theme } = useTheme();
  
  // Calculate progress for countdown mode
  const getProgress = () => {
    if (mode !== TIMER_MODES.COUNTDOWN || initialTime === 0) return 0;
    return ((initialTime - timer) / initialTime) * 100;
  };

  const getModeConfig = () => {
    switch (mode) {
      case TIMER_MODES.COUNTDOWN:
        return { 
          icon: '⏰', 
          name: 'Countdown',
          color: 'from-red-500 to-orange-500',
          bgColor: 'bg-red-50 dark:bg-red-900/20'
        };
      case TIMER_MODES.POMODORO:
        return { 
          icon: '🍅', 
          name: '<PERSON>modor<PERSON>',
          color: 'from-red-500 to-pink-500',
          bgColor: 'bg-red-50 dark:bg-red-900/20'
        };
      default:
        return { 
          icon: '⏱️', 
          name: 'Stopwatch',
          color: 'from-blue-500 to-purple-500',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20'
        };
    }
  };

  const modeConfig = getModeConfig();
  const progress = getProgress();
  const timeComponents = formatTimeDisplay(timer).split(':');

  return (
    <div className="text-center mb-8">
      {/* Mode Header */}
      <div className={`inline-flex items-center gap-3 px-6 py-3 rounded-full mb-6 ${modeConfig.bgColor}`}>
        <span className={`text-2xl ${isRunning ? "animate-pulse" : ""}`}>
          {modeConfig.icon}
        </span>
        <span className={`font-semibold text-lg ${theme.colors.text}`}>
          {modeConfig.name} Mode
        </span>
      </div>

      {/* Circular Progress for Countdown */}
      {mode === TIMER_MODES.COUNTDOWN && initialTime > 0 && (
        <div className="relative w-80 h-80 mx-auto mb-6">
          {/* Background Circle */}
          <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-gray-200 dark:text-gray-700"
            />
            {/* Progress Circle */}
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="url(#gradient)"
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 45}`}
              strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}
              className="transition-all duration-1000 ease-out"
            />
            {/* Gradient Definition */}
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3B82F6" />
                <stop offset="100%" stopColor="#8B5CF6" />
              </linearGradient>
            </defs>
          </svg>
          
          {/* Timer Display in Center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="font-mono text-4xl font-bold text-gray-800 dark:text-white mb-2">
                {formatTimeDisplay(timer)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round(progress)}% complete
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Time Display for Stopwatch/Pomodoro */}
      {mode !== TIMER_MODES.COUNTDOWN && (
        <div className="mb-6">
          {/* Digital Clock Style */}
          <div className="flex items-center justify-center gap-2 mb-4">
            {timeComponents.map((component, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className={`
                  bg-gradient-to-br ${modeConfig.color} text-white
                  px-4 py-6 rounded-2xl shadow-lg min-w-[80px]
                  font-mono text-3xl font-bold
                  transform transition-all duration-300
                  ${isRunning ? 'scale-105' : 'scale-100'}
                `}>
                  {component}
                </div>
                {index < timeComponents.length - 1 && (
                  <span className="text-2xl font-bold text-gray-400 animate-pulse">
                    :
                  </span>
                )}
              </div>
            ))}
          </div>
          
          {/* Time Labels */}
          <div className="flex items-center justify-center gap-8 text-sm text-gray-500 dark:text-gray-400">
            <span>Hours</span>
            <span>Minutes</span>
            <span>Seconds</span>
          </div>
        </div>
      )}

      {/* Simple Time Display for Countdown in Circle */}
      {mode === TIMER_MODES.COUNTDOWN && initialTime > 0 && (
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Started with {formatTimeDisplay(initialTime)}
        </div>
      )}

      {/* Pulsing Animation for Running State */}
      {isRunning && (
        <div className="absolute inset-0 pointer-events-none">
          <div className={`
            absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
            w-96 h-96 rounded-full opacity-20
            bg-gradient-to-r ${modeConfig.color}
            animate-ping
          `} />
        </div>
      )}
    </div>
  );
};

export default EnhancedTimer;
