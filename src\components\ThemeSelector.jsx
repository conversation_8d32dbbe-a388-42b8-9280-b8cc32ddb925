import { useState } from 'react';
import { useTheme, THEMES } from '../contexts/ThemeContext';
import Button from './Button';

const ThemeSelector = () => {
  const { currentTheme, theme, themes, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const handleThemeChange = (themeKey) => {
    setTheme(themeKey);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
          ${theme.colors.secondary} ${theme.colors.text}
        `}
        ariaLabel="Change theme"
      >
        <span>{themes[currentTheme].icon}</span>
        <span className="text-sm font-medium">{themes[currentTheme].name}</span>
        <span className={`transform transition-transform ${isOpen ? 'rotate-180' : ''}`}>
          ▼
        </span>
      </Button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className={`
            absolute top-full left-0 mt-2 py-2 rounded-lg z-20 min-w-full
            ${theme.colors.cardBg} ${theme.colors.border} ${theme.colors.shadow}
            border
          `}>
            {Object.entries(themes).map(([key, themeData]) => (
              <button
                key={key}
                onClick={() => handleThemeChange(key)}
                className={`
                  w-full px-4 py-2 text-left flex items-center gap-3 transition-colors
                  ${currentTheme === key 
                    ? `${theme.colors.primary.split(' ')[0]} text-white` 
                    : `hover:${theme.colors.secondary.split(' ')[0]} ${theme.colors.text}`
                  }
                `}
              >
                <span className="text-lg">{themeData.icon}</span>
                <span className="font-medium">{themeData.name}</span>
                {currentTheme === key && (
                  <span className="ml-auto">✓</span>
                )}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default ThemeSelector;
