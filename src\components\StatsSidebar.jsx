import { useTimer } from '../contexts/TimerContext';
import { useTheme } from '../contexts/ThemeContext';
import { formatTimeDisplay } from '../utils/timeUtils';

const StatsSidebar = () => {
  const { lapTimes, history, timer, mode } = useTimer();
  const { theme } = useTheme();

  // Mock statistics - in a real app, these would be calculated from history
  const stats = {
    todayTime: timer,
    weekTime: timer * 7,
    totalSessions: history.length + lapTimes.length,
    averageSession: timer > 0 ? timer : 1500, // 25 minutes default
    streak: 3
  };

  return (
    <div className={`${theme.colors.cardBg} rounded-2xl ${theme.colors.shadow} p-6`}>
      {/* Statistics Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
          <span className="text-white text-sm">📊</span>
        </div>
        <h3 className={`text-lg font-bold ${theme.colors.text}`}>
          Statistics
        </h3>
      </div>

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className={`p-4 rounded-xl ${theme.colors.secondary}`}>
          <div className={`text-2xl font-bold ${theme.colors.text}`}>
            {formatTimeDisplay(stats.todayTime)}
          </div>
          <div className={`text-xs ${theme.colors.textMuted}`}>
            Today
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${theme.colors.secondary}`}>
          <div className={`text-2xl font-bold ${theme.colors.text}`}>
            {stats.totalSessions}
          </div>
          <div className={`text-xs ${theme.colors.textMuted}`}>
            Sessions
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${theme.colors.secondary}`}>
          <div className={`text-2xl font-bold ${theme.colors.text}`}>
            {stats.streak}
          </div>
          <div className={`text-xs ${theme.colors.textMuted}`}>
            Day Streak
          </div>
        </div>
        
        <div className={`p-4 rounded-xl ${theme.colors.secondary}`}>
          <div className={`text-2xl font-bold ${theme.colors.text}`}>
            {formatTimeDisplay(stats.averageSession)}
          </div>
          <div className={`text-xs ${theme.colors.textMuted}`}>
            Average
          </div>
        </div>
      </div>

      {/* Weekly Progress */}
      <div className="mb-6">
        <h4 className={`text-sm font-semibold ${theme.colors.text} mb-3`}>
          This Week
        </h4>
        <div className="space-y-2">
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
            <div key={day} className="flex items-center gap-3">
              <span className={`text-xs w-8 ${theme.colors.textMuted}`}>
                {day}
              </span>
              <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-500`}
                  style={{ width: `${Math.random() * 100}%` }}
                />
              </div>
              <span className={`text-xs ${theme.colors.textMuted} w-12 text-right`}>
                {Math.floor(Math.random() * 8)}h
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Lap Times */}
      {lapTimes.length > 0 && (
        <div>
          <h4 className={`text-sm font-semibold ${theme.colors.text} mb-3`}>
            Recent Laps
          </h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {lapTimes.slice(-5).reverse().map((lap, index) => (
              <div
                key={lap.timestamp}
                className={`flex justify-between items-center p-2 rounded-lg ${theme.colors.secondary}`}
              >
                <span className={`text-sm ${theme.colors.textSecondary}`}>
                  Lap {lapTimes.length - index}
                </span>
                <span className={`text-sm font-mono ${theme.colors.text}`}>
                  {formatTimeDisplay(lap.time)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Achievement Badge */}
      <div className="mt-6 p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl text-white">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-lg">🏆</span>
          <span className="font-semibold">Achievement</span>
        </div>
        <div className="text-sm opacity-90">
          First Timer Session!
        </div>
      </div>
    </div>
  );
};

export default StatsSidebar;
