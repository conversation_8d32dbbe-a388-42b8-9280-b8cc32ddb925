import { useState, useEffect } from 'react';
import { useTimer, TIMER_MODES } from '../contexts/TimerContext';
import { getTimeComponents, validateTimeInput } from '../utils/timeUtils';
import Button from './Button';

const TimeInput = () => {
  const { timer, initialTime, mode, dispatch, actions, isRunning } = useTimer();
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isEditing, setIsEditing] = useState(false);

  // Update local state when timer changes
  useEffect(() => {
    const timeToUse = mode === TIMER_MODES.COUNTDOWN ? initialTime : timer;
    const components = getTimeComponents(timeToUse);
    setHours(components.hours);
    setMinutes(components.minutes);
    setSeconds(components.seconds);
  }, [timer, initialTime, mode]);

  const handleInputChange = (value, setter, max) => {
    const numValue = Math.max(0, Math.min(max, parseInt(value) || 0));
    setter(numValue);
  };

  const handleApply = () => {
    if (!validateTimeInput(hours, minutes, seconds)) {
      alert('Please enter valid time values');
      return;
    }

    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    
    if (mode === TIMER_MODES.COUNTDOWN) {
      dispatch({ type: actions.SET_INITIAL_TIME, payload: totalSeconds });
    } else {
      dispatch({ type: actions.SET_TIMER, payload: totalSeconds });
    }
    
    setIsEditing(false);
  };

  const handleCancel = () => {
    // Reset to current values
    const timeToUse = mode === TIMER_MODES.COUNTDOWN ? initialTime : timer;
    const components = getTimeComponents(timeToUse);
    setHours(components.hours);
    setMinutes(components.minutes);
    setSeconds(components.seconds);
    setIsEditing(false);
  };

  if (!isEditing) {
    return (
      <div className="flex items-center justify-center gap-2 mb-4">
        <Button
          onClick={() => setIsEditing(true)}
          disabled={isRunning}
          className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded transition-colors"
          ariaLabel="Edit time"
        >
          ✏️ Set Time
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 p-4 rounded-lg mb-4">
      <h3 className="text-sm font-medium text-gray-700 mb-3 text-center">
        Set {mode === TIMER_MODES.COUNTDOWN ? 'Countdown' : 'Timer'} Time
      </h3>
      
      <div className="flex items-center justify-center gap-2 mb-4">
        {/* Hours */}
        <div className="text-center">
          <input
            type="number"
            min="0"
            max="23"
            value={hours}
            onChange={(e) => handleInputChange(e.target.value, setHours, 23)}
            className="w-16 p-2 text-center border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Hours"
          />
          <div className="text-xs text-gray-500 mt-1">Hours</div>
        </div>
        
        <span className="text-2xl font-bold text-gray-400">:</span>
        
        {/* Minutes */}
        <div className="text-center">
          <input
            type="number"
            min="0"
            max="59"
            value={minutes}
            onChange={(e) => handleInputChange(e.target.value, setMinutes, 59)}
            className="w-16 p-2 text-center border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Minutes"
          />
          <div className="text-xs text-gray-500 mt-1">Minutes</div>
        </div>
        
        <span className="text-2xl font-bold text-gray-400">:</span>
        
        {/* Seconds */}
        <div className="text-center">
          <input
            type="number"
            min="0"
            max="59"
            value={seconds}
            onChange={(e) => handleInputChange(e.target.value, setSeconds, 59)}
            className="w-16 p-2 text-center border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Seconds"
          />
          <div className="text-xs text-gray-500 mt-1">Seconds</div>
        </div>
      </div>
      
      <div className="flex gap-2 justify-center">
        <Button
          onClick={handleApply}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          Apply
        </Button>
        <Button
          onClick={handleCancel}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default TimeInput;
