import Button from "./Button";
import { useRef, useEffect } from "react";
import { TIMER_MODES } from "../contexts/TimerContext";

const TimerControls = ({
  toggleTimer,
  resetTimer,
  isRunning,
  timer,
  mode,
  initialTime,
}) => {
  const buttonRef = useRef(null);

  useEffect(() => {
    if (buttonRef.current) {
      buttonRef.current.focus();
    }
  }, []);

  const canReset = () => {
    if (mode === TIMER_MODES.COUNTDOWN) {
      return timer !== initialTime;
    }
    return timer !== 0;
  };

  return (
    <div className="flex flex-wrap gap-3 justify-center">
      <Button
        ref={buttonRef}
        onClick={toggleTimer}
        className={`
          px-8 py-3 rounded-lg font-semibold text-white transition-all duration-200 shadow-md
          ${
            isRunning
              ? "bg-orange-500 hover:bg-orange-600"
              : "bg-green-500 hover:bg-green-600"
          }
        `}
        ariaLabel={isRunning ? "Pause timer" : "Start timer"}
      >
        {isRunning ? "⏸️ Pause" : "▶️ Start"}
      </Button>

      <Button
        disabled={!canReset()}
        onClick={resetTimer}
        className={`
          px-8 py-3 rounded-lg font-semibold transition-all duration-200 shadow-md
          ${
            canReset()
              ? "bg-red-500 text-white hover:bg-red-600"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }
        `}
        ariaLabel="Reset timer"
      >
        🔄 Reset
      </Button>
    </div>
  );
};

export default TimerControls;
