import Button from "./Button";
import { useRef, useEffect } from "react";

const TimerControls = ({ toggleTimer, setTimer, isRunning, timer }) => {
  const buttonRef = useRef(null);
  useEffect(() => {
    if (buttonRef.current) {
      buttonRef.current.focus();
    }
  }, []);

  return (
    <>
      <Button
        ref={buttonRef}
        onClick={toggleTimer}
        className="rounded mt-3 px-6 py-1 bg-green-500 text-white hover:bg-green-600 cursor-pointer transition-all duration-500"
      >
        {isRunning ? "Pause" : "Start"}
      </Button>
      <Button
        disabled={isRunning || timer === 0 ? true : false}
        onClick={() => setTimer(0)}
        className="rounded ml-3 mt-3 px-6 py-1 bg-red-500 text-white hover:bg-red-600 cursor-pointer transition-all duration-500"
      >
        Reset
      </Button>
    </>
  );
};

export default TimerControls;
