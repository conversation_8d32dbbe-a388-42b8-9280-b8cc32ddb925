import { useTimer, TIMER_MODES } from '../contexts/TimerContext';
import { useTheme } from '../contexts/ThemeContext';
import Button from './Button';

const EnhancedModeSelector = () => {
  const { mode, dispatch, actions, isRunning } = useTimer();
  const { theme } = useTheme();

  const handleModeChange = (newMode) => {
    if (isRunning) return; // Don't allow mode change while running
    dispatch({ type: actions.SET_MODE, payload: newMode });
  };

  const modes = [
    { 
      key: TIMER_MODES.STOPWATCH, 
      label: 'Stopwatch', 
      icon: '⏱️',
      description: 'Count up from zero',
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50'
    },
    { 
      key: TIMER_MODES.COUNTDOWN, 
      label: 'Countdown', 
      icon: '⏰',
      description: 'Count down to zero',
      gradient: 'from-orange-500 to-red-500',
      bgGradient: 'from-orange-50 to-red-50'
    },
    { 
      key: TIMER_MODES.POMODORO, 
      label: 'Pomodoro', 
      icon: '🍅',
      description: 'Work & break cycles',
      gradient: 'from-red-500 to-pink-500',
      bgGradient: 'from-red-50 to-pink-50'
    }
  ];

  return (
    <div className="mb-8">
      <h3 className={`text-center text-sm font-medium ${theme.colors.textMuted} mb-4`}>
        Choose Timer Mode
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
        {modes.map(({ key, label, icon, description, gradient, bgGradient }) => (
          <Button
            key={key}
            onClick={() => handleModeChange(key)}
            disabled={isRunning}
            className={`
              group relative p-6 rounded-2xl transition-all duration-300 transform
              ${mode === key 
                ? `bg-gradient-to-br ${gradient} text-white shadow-xl scale-105 ring-4 ring-white/30` 
                : `bg-gradient-to-br ${bgGradient} ${theme.colors.text} hover:scale-105 shadow-lg hover:shadow-xl`
              }
              ${isRunning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-2xl'}
            `}
            ariaLabel={`Switch to ${label} mode`}
          >
            {/* Selection Indicator */}
            {mode === key && (
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-lg">
                <span className="text-green-500 text-sm">✓</span>
              </div>
            )}
            
            {/* Icon */}
            <div className="text-4xl mb-3 text-center">
              {icon}
            </div>
            
            {/* Label */}
            <div className="text-lg font-bold text-center mb-2">
              {label}
            </div>
            
            {/* Description */}
            <div className={`text-sm text-center opacity-80 ${mode === key ? 'text-white' : theme.colors.textSecondary}`}>
              {description}
            </div>
            
            {/* Keyboard Shortcut */}
            <div className="absolute bottom-2 right-2 opacity-60">
              <span className={`text-xs px-2 py-1 rounded ${mode === key ? 'bg-white/20' : 'bg-black/10'}`}>
                {key === TIMER_MODES.STOPWATCH ? '1' : key === TIMER_MODES.COUNTDOWN ? '2' : '3'}
              </span>
            </div>
            
            {/* Hover Effect */}
            <div className="absolute inset-0 rounded-2xl bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </Button>
        ))}
      </div>
    </div>
  );
};

export default EnhancedModeSelector;
