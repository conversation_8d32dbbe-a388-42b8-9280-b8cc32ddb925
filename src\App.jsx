import { useState, useRef, useEffect } from "react";
import TimerControls from "./components/TimerControls";
import Timer from "./components/Timer";

function App() {
  const [timer, setTimer] = useState(() => {
    const saved = localStorage.getItem("timer");
    return saved ? parseInt(saved) : 0;
  });
  const refTimer = useRef(null);
  const [isRunning, setIsRunning] = useState(false);

  useEffect(() => {
    localStorage.setItem("timer", timer);
  }, [timer]);

  const toggleTimer = () => {
    if (isRunning) {
      clearInterval(refTimer.current);
      refTimer.current = null;
    } else {
      refTimer.current = setInterval(() => {
        setTimer((prevTimer) => {
          return prevTimer + 1;
        });
      }, 1000);
    }

    setIsRunning(!isRunning);
  };

  return (
    <div className="max-w-md mx-auto mt-10 bg-white p-6 rounded-md shadow-lg text-center">
      <h1 className="text-2xl font-semibold">Counter App</h1>
      <Timer
        timer={timer}
        isRunning={isRunning}
        toggleTimer={toggleTimer}
        setTimer={setTimer}
      />
      <TimerControls
        toggleTimer={toggleTimer}
        setTimer={setTimer}
        isRunning={isRunning}
        timer={timer}
      />
    </div>
  );
}

export default App;
