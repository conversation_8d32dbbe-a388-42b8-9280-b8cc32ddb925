import { useEffect } from 'react';

/**
 * Custom hook for handling keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to functions
 * @param {boolean} enabled - Whether shortcuts are enabled (default: true)
 */
export const useKeyboardShortcuts = (shortcuts, enabled = true) => {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event) => {
      // Don't trigger shortcuts when typing in inputs
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      const key = event.key.toLowerCase();
      const ctrl = event.ctrlKey;
      const alt = event.altKey;
      const shift = event.shiftKey;
      const meta = event.metaKey;

      // Create key combination string
      let combination = '';
      if (ctrl) combination += 'ctrl+';
      if (alt) combination += 'alt+';
      if (shift) combination += 'shift+';
      if (meta) combination += 'meta+';
      combination += key;

      // Also check for simple key
      const simpleKey = key;

      // Check if we have a handler for this combination
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination](event);
      } else if (shortcuts[simpleKey]) {
        event.preventDefault();
        shortcuts[simpleKey](event);
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts, enabled]);
};

/**
 * Default keyboard shortcuts for timer app
 */
export const getDefaultShortcuts = (actions) => ({
  // Space - Start/Pause timer
  ' ': actions.toggleTimer,
  'spacebar': actions.toggleTimer,
  
  // R - Reset timer
  'r': actions.resetTimer,
  
  // L - Add lap time
  'l': actions.addLap,
  
  // Escape - Exit fullscreen or close modals
  'escape': actions.escape,
  
  // F - Toggle fullscreen
  'f': actions.toggleFullscreen,
  
  // S - Open settings
  's': actions.openSettings,
  
  // T - Change theme
  't': actions.changeTheme,
  
  // Numbers 1-3 for mode switching
  '1': () => actions.setMode('stopwatch'),
  '2': () => actions.setMode('countdown'),
  '3': () => actions.setMode('pomodoro'),
  
  // Arrow keys for time adjustment (when not running)
  'arrowup': actions.increaseTime,
  'arrowdown': actions.decreaseTime,
  
  // Ctrl+S - Save/Export data
  'ctrl+s': actions.exportData,
  
  // Ctrl+O - Import data
  'ctrl+o': actions.importData,
  
  // ? - Show help
  '?': actions.showHelp,
  '/': actions.showHelp
});

/**
 * Hook specifically for timer keyboard shortcuts
 */
export const useTimerShortcuts = (timerActions, enabled = true) => {
  const shortcuts = getDefaultShortcuts(timerActions);
  useKeyboardShortcuts(shortcuts, enabled);
};
