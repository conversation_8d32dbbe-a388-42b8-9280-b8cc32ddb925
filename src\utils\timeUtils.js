/**
 * Utility functions for time formatting and manipulation
 */

/**
 * Format seconds into HH:MM:SS format
 * @param {number} totalSeconds - Total seconds to format
 * @returns {string} Formatted time string (HH:MM:SS)
 */
export const formatTime = (totalSeconds) => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Parse time string (HH:MM:SS) into total seconds
 * @param {string} timeString - Time string in HH:MM:SS format
 * @returns {number} Total seconds
 */
export const parseTimeString = (timeString) => {
  const parts = timeString.split(':').map(part => parseInt(part, 10));
  if (parts.length !== 3) return 0;
  
  const [hours, minutes, seconds] = parts;
  return (hours * 3600) + (minutes * 60) + seconds;
};

/**
 * Validate time input values
 * @param {number} hours - Hours (0-23)
 * @param {number} minutes - Minutes (0-59)
 * @param {number} seconds - Seconds (0-59)
 * @returns {boolean} True if valid
 */
export const validateTimeInput = (hours, minutes, seconds) => {
  return (
    hours >= 0 && hours <= 23 &&
    minutes >= 0 && minutes <= 59 &&
    seconds >= 0 && seconds <= 59
  );
};

/**
 * Get time components from total seconds
 * @param {number} totalSeconds - Total seconds
 * @returns {object} Object with hours, minutes, seconds
 */
export const getTimeComponents = (totalSeconds) => {
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  return { hours, minutes, seconds };
};

/**
 * Format time for display with optional compact mode
 * @param {number} totalSeconds - Total seconds to format
 * @param {boolean} compact - Whether to use compact format (hide leading zeros)
 * @returns {string} Formatted time string
 */
export const formatTimeDisplay = (totalSeconds, compact = false) => {
  const { hours, minutes, seconds } = getTimeComponents(totalSeconds);
  
  if (compact && hours === 0) {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  
  return formatTime(totalSeconds);
};
