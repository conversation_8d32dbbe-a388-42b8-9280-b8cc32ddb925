import { useTimer, TIMER_MODES } from '../contexts/TimerContext';
import { useTheme } from '../contexts/ThemeContext';
import Button from './Button';

const FloatingControls = ({ toggleTimer, resetTimer, isRunning, timer, mode, initialTime }) => {
  const { dispatch, actions } = useTimer();
  const { theme } = useTheme();

  const canReset = () => {
    if (mode === TIMER_MODES.COUNTDOWN) {
      return timer !== initialTime;
    }
    return timer !== 0;
  };

  const addLap = () => {
    if (isRunning) {
      dispatch({ type: actions.ADD_LAP_TIME });
    }
  };

  return (
    <div className="flex items-center justify-center gap-4 mb-8">
      {/* Reset Button */}
      <Button
        disabled={!canReset()}
        onClick={resetTimer}
        className={`
          group relative w-16 h-16 rounded-full transition-all duration-300 shadow-lg
          ${canReset()
            ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white hover:shadow-xl hover:scale-110' 
            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          }
        `}
        ariaLabel="Reset timer"
      >
        <span className="text-xl">🔄</span>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
          <span className="text-xs bg-black text-white px-2 py-1 rounded whitespace-nowrap">
            Reset (R)
          </span>
        </div>
      </Button>

      {/* Main Play/Pause Button */}
      <Button
        onClick={toggleTimer}
        className={`
          group relative w-20 h-20 rounded-full transition-all duration-300 shadow-xl
          ${isRunning 
            ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600' 
            : 'bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600'
          }
          text-white hover:shadow-2xl hover:scale-110 transform
        `}
        ariaLabel={isRunning ? "Pause timer" : "Start timer"}
      >
        <span className="text-2xl">
          {isRunning ? "⏸️" : "▶️"}
        </span>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
          <span className="text-xs bg-black text-white px-2 py-1 rounded whitespace-nowrap">
            {isRunning ? "Pause" : "Start"} (Space)
          </span>
        </div>
      </Button>

      {/* Lap Button */}
      <Button
        disabled={!isRunning}
        onClick={addLap}
        className={`
          group relative w-16 h-16 rounded-full transition-all duration-300 shadow-lg
          ${isRunning
            ? 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white hover:shadow-xl hover:scale-110' 
            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          }
        `}
        ariaLabel="Add lap time"
      >
        <span className="text-xl">📍</span>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
          <span className="text-xs bg-black text-white px-2 py-1 rounded whitespace-nowrap">
            Lap (L)
          </span>
        </div>
      </Button>
    </div>
  );
};

export default FloatingControls;
