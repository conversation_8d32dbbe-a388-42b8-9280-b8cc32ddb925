import { useState } from 'react';
import { useTimer } from '../contexts/TimerContext';
import { useTheme } from '../contexts/ThemeContext';
import Button from './Button';

const PomodoroSettings = () => {
  const { settings, dispatch, actions } = useTimer();
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  
  // Default Pomodoro settings
  const defaultSettings = {
    workDuration: 25 * 60, // 25 minutes
    shortBreakDuration: 5 * 60, // 5 minutes
    longBreakDuration: 15 * 60, // 15 minutes
    sessionsUntilLongBreak: 4,
    autoStartBreaks: true,
    autoStartWork: false,
    ...settings.pomodoro
  };

  const [pomodoroSettings, setPomodoroSettings] = useState(defaultSettings);

  const handleSettingChange = (key, value) => {
    const newSettings = { ...pomodoroSettings, [key]: value };
    setPomodoroSettings(newSettings);
    
    dispatch({
      type: actions.UPDATE_SETTINGS,
      payload: { 
        pomodoro: newSettings
      }
    });
  };

  const formatMinutes = (seconds) => Math.floor(seconds / 60);
  const handleMinuteChange = (key, minutes) => {
    handleSettingChange(key, minutes * 60);
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={`
          px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
          ${theme.colors.secondary} ${theme.colors.text}
        `}
        ariaLabel="Pomodoro settings"
      >
        🍅 Pomodoro Settings
      </Button>
    );
  }

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-20" 
        onClick={() => setIsOpen(false)}
      />
      
      {/* Settings Modal */}
      <div className={`
        fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30
        w-full max-w-md mx-4 p-6 rounded-xl
        ${theme.colors.cardBg} ${theme.colors.shadow}
      `}>
        <div className="flex justify-between items-center mb-6">
          <h2 className={`text-xl font-bold ${theme.colors.text}`}>
            🍅 Pomodoro Settings
          </h2>
          <Button
            onClick={() => setIsOpen(false)}
            className={`
              px-3 py-1 rounded-lg transition-colors
              ${theme.colors.secondary} ${theme.colors.text}
            `}
            ariaLabel="Close settings"
          >
            ✕
          </Button>
        </div>

        <div className="space-y-6">
          {/* Duration Settings */}
          <div>
            <h3 className={`text-lg font-semibold ${theme.colors.text} mb-3`}>
              ⏱️ Durations
            </h3>
            
            <div className="space-y-4">
              {/* Work Duration */}
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Work Session
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="1"
                    max="60"
                    value={formatMinutes(pomodoroSettings.workDuration)}
                    onChange={(e) => handleMinuteChange('workDuration', parseInt(e.target.value) || 25)}
                    className={`
                      w-16 px-2 py-1 rounded border text-center
                      ${theme.colors.cardBg} ${theme.colors.text} ${theme.colors.border}
                    `}
                  />
                  <span className={`text-sm ${theme.colors.textMuted}`}>min</span>
                </div>
              </div>

              {/* Short Break Duration */}
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Short Break
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="1"
                    max="30"
                    value={formatMinutes(pomodoroSettings.shortBreakDuration)}
                    onChange={(e) => handleMinuteChange('shortBreakDuration', parseInt(e.target.value) || 5)}
                    className={`
                      w-16 px-2 py-1 rounded border text-center
                      ${theme.colors.cardBg} ${theme.colors.text} ${theme.colors.border}
                    `}
                  />
                  <span className={`text-sm ${theme.colors.textMuted}`}>min</span>
                </div>
              </div>

              {/* Long Break Duration */}
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Long Break
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="1"
                    max="60"
                    value={formatMinutes(pomodoroSettings.longBreakDuration)}
                    onChange={(e) => handleMinuteChange('longBreakDuration', parseInt(e.target.value) || 15)}
                    className={`
                      w-16 px-2 py-1 rounded border text-center
                      ${theme.colors.cardBg} ${theme.colors.text} ${theme.colors.border}
                    `}
                  />
                  <span className={`text-sm ${theme.colors.textMuted}`}>min</span>
                </div>
              </div>

              {/* Sessions until long break */}
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Sessions until long break
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="2"
                    max="10"
                    value={pomodoroSettings.sessionsUntilLongBreak}
                    onChange={(e) => handleSettingChange('sessionsUntilLongBreak', parseInt(e.target.value) || 4)}
                    className={`
                      w-16 px-2 py-1 rounded border text-center
                      ${theme.colors.cardBg} ${theme.colors.text} ${theme.colors.border}
                    `}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Auto-start Settings */}
          <div>
            <h3 className={`text-lg font-semibold ${theme.colors.text} mb-3`}>
              🔄 Auto-start
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Auto-start breaks
                </label>
                <input
                  type="checkbox"
                  checked={pomodoroSettings.autoStartBreaks}
                  onChange={(e) => handleSettingChange('autoStartBreaks', e.target.checked)}
                  className="w-4 h-4"
                />
              </div>

              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Auto-start work sessions
                </label>
                <input
                  type="checkbox"
                  checked={pomodoroSettings.autoStartWork}
                  onChange={(e) => handleSettingChange('autoStartWork', e.target.checked)}
                  className="w-4 h-4"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <Button
            onClick={() => setIsOpen(false)}
            className={`
              w-full py-2 rounded-lg font-medium transition-colors
              ${theme.colors.primary} text-white
            `}
          >
            Save Settings
          </Button>
        </div>
      </div>
    </>
  );
};

export default PomodoroSettings;
