import { createContext, useContext, useReducer, useEffect } from "react";

// Timer modes
export const TIMER_MODES = {
  STOPWATCH: "stopwatch",
  COUNTDOWN: "countdown",
  POMODORO: "pomodoro",
};

// Timer intervals (in milliseconds)
export const TIMER_INTERVALS = {
  FAST: 100, // 0.1 seconds
  NORMAL: 1000, // 1 second
  SLOW: 10000, // 10 seconds
};

// Initial state
const initialState = {
  timer: 0,
  initialTime: 0,
  isRunning: false,
  mode: TIMER_MODES.STOPWATCH,
  interval: TIMER_INTERVALS.NORMAL,
  lapTimes: [],
  history: [],
  settings: {
    soundEnabled: true,
    soundType: "chime",
    notificationsEnabled: true,
    tickSoundEnabled: false,
    theme: "light",
    compactDisplay: false,
    pomodoro: {
      workDuration: 25 * 60,
      shortBreakDuration: 5 * 60,
      longBreakDuration: 15 * 60,
      sessionsUntilLongBreak: 4,
      autoStartBreaks: true,
      autoStartWork: false,
    },
  },
  pomodoroState: {
    currentSession: 1,
    isBreak: false,
    completedSessions: 0,
    sessionType: "work", // 'work', 'shortBreak', 'longBreak'
  },
};

// Action types
const TIMER_ACTIONS = {
  SET_TIMER: "SET_TIMER",
  SET_INITIAL_TIME: "SET_INITIAL_TIME",
  TOGGLE_TIMER: "TOGGLE_TIMER",
  RESET_TIMER: "RESET_TIMER",
  SET_MODE: "SET_MODE",
  SET_INTERVAL: "SET_INTERVAL",
  ADD_LAP_TIME: "ADD_LAP_TIME",
  CLEAR_LAP_TIMES: "CLEAR_LAP_TIMES",
  ADD_TO_HISTORY: "ADD_TO_HISTORY",
  UPDATE_SETTINGS: "UPDATE_SETTINGS",
  LOAD_STATE: "LOAD_STATE",
};

// Reducer function
const timerReducer = (state, action) => {
  switch (action.type) {
    case TIMER_ACTIONS.SET_TIMER:
      return { ...state, timer: action.payload };

    case TIMER_ACTIONS.SET_INITIAL_TIME:
      return { ...state, initialTime: action.payload, timer: action.payload };

    case TIMER_ACTIONS.TOGGLE_TIMER:
      return { ...state, isRunning: !state.isRunning };

    case TIMER_ACTIONS.RESET_TIMER:
      return {
        ...state,
        timer: state.mode === TIMER_MODES.COUNTDOWN ? state.initialTime : 0,
        isRunning: false,
        lapTimes: [],
      };

    case TIMER_ACTIONS.SET_MODE:
      return {
        ...state,
        mode: action.payload,
        timer: action.payload === TIMER_MODES.COUNTDOWN ? state.initialTime : 0,
        isRunning: false,
        lapTimes: [],
      };

    case TIMER_ACTIONS.SET_INTERVAL:
      return { ...state, interval: action.payload };

    case TIMER_ACTIONS.ADD_LAP_TIME:
      return {
        ...state,
        lapTimes: [
          ...state.lapTimes,
          { time: state.timer, timestamp: Date.now() },
        ],
      };

    case TIMER_ACTIONS.CLEAR_LAP_TIMES:
      return { ...state, lapTimes: [] };

    case TIMER_ACTIONS.ADD_TO_HISTORY:
      return {
        ...state,
        history: [...state.history, action.payload],
      };

    case TIMER_ACTIONS.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case TIMER_ACTIONS.LOAD_STATE:
      return { ...state, ...action.payload };

    default:
      return state;
  }
};

// Context
const TimerContext = createContext();

// Provider component
export const TimerProvider = ({ children }) => {
  const [state, dispatch] = useReducer(timerReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem("timerAppState");
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: TIMER_ACTIONS.LOAD_STATE, payload: parsedState });
      } catch (error) {
        console.error("Failed to load saved state:", error);
      }
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    const stateToSave = {
      timer: state.timer,
      initialTime: state.initialTime,
      mode: state.mode,
      interval: state.interval,
      lapTimes: state.lapTimes,
      history: state.history,
      settings: state.settings,
    };
    localStorage.setItem("timerAppState", JSON.stringify(stateToSave));
  }, [state]);

  const value = {
    ...state,
    dispatch,
    actions: TIMER_ACTIONS,
  };

  return (
    <TimerContext.Provider value={value}>{children}</TimerContext.Provider>
  );
};

// Custom hook to use timer context
export const useTimer = () => {
  const context = useContext(TimerContext);
  if (!context) {
    throw new Error("useTimer must be used within a TimerProvider");
  }
  return context;
};
