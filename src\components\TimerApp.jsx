import { useRef, useEffect } from 'react';
import { useTimer, TIMER_MODES } from '../contexts/TimerContext';
import { useTheme } from '../contexts/ThemeContext';
import Timer from './Timer';
import TimerControls from './TimerControls';
import ModeSelector from './ModeSelector';
import TimeInput from './TimeInput';
import TimerPresets from './TimerPresets';
import LapTimes from './LapTimes';
import ThemeSelector from './ThemeSelector';
import Settings from './Settings';
import Help from './Help';
import { playSound, playTick, SOUND_TYPES } from '../utils/soundUtils';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

const TimerApp = () => {
  const { 
    timer, 
    initialTime, 
    isRunning, 
    mode, 
    interval, 
    dispatch, 
    actions,
    settings 
  } = useTimer();
  
  const { theme } = useTheme();
  const intervalRef = useRef(null);

  // Timer logic
  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        // Play tick sound if enabled
        if (settings.tickSoundEnabled && settings.soundEnabled) {
          playTick(0.1);
        }
        
        dispatch({ 
          type: actions.SET_TIMER, 
          payload: mode === TIMER_MODES.COUNTDOWN 
            ? Math.max(0, timer - 1)
            : timer + 1
        });
      }, interval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isRunning, timer, mode, interval, dispatch, actions, settings]);

  // Stop timer when countdown reaches 0
  useEffect(() => {
    if (mode === TIMER_MODES.COUNTDOWN && timer === 0 && isRunning) {
      dispatch({ type: actions.TOGGLE_TIMER });
      
      // Play sound notification if enabled
      if (settings.soundEnabled) {
        playSound(settings.soundType || SOUND_TYPES.CHIME, 0.5);
      }
      
      // Show browser notification if enabled
      if (settings.notificationsEnabled && 'Notification' in window) {
        if (Notification.permission === 'granted') {
          new Notification('Timer Finished!', {
            body: 'Your countdown timer has reached zero.',
            icon: '/vite.svg'
          });
        }
      }
    }
  }, [timer, mode, isRunning, dispatch, actions, settings]);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const toggleTimer = () => {
    dispatch({ type: actions.TOGGLE_TIMER });
  };

  const resetTimer = () => {
    dispatch({ type: actions.RESET_TIMER });
  };

  const addLap = () => {
    if (isRunning) {
      dispatch({ type: actions.ADD_LAP_TIME });
    }
  };

  // Keyboard shortcuts
  const shortcuts = {
    ' ': toggleTimer,
    'spacebar': toggleTimer,
    'r': resetTimer,
    'l': addLap,
    '1': () => !isRunning && dispatch({ type: actions.SET_MODE, payload: 'stopwatch' }),
    '2': () => !isRunning && dispatch({ type: actions.SET_MODE, payload: 'countdown' }),
    '3': () => !isRunning && dispatch({ type: actions.SET_MODE, payload: 'pomodoro' }),
  };

  useKeyboardShortcuts(shortcuts);

  return (
    <div className={`min-h-screen ${theme.colors.bg} py-8`}>
      <div className="max-w-2xl mx-auto">
        <div className={`${theme.colors.cardBg} rounded-xl ${theme.colors.shadow} p-6`}>
          <div className="flex justify-between items-start mb-6">
            <div className="text-center flex-1">
              <h1 className={`text-3xl font-bold ${theme.colors.text} mb-2`}>
                Advanced Timer App
              </h1>
              <p className={theme.colors.textSecondary}>
                Stopwatch • Countdown • Pomodoro
              </p>
            </div>
            <div className="flex gap-2">
              <Help />
              <Settings />
              <ThemeSelector />
            </div>
          </div>

          <ModeSelector />

          <div className="space-y-4">
            <Timer timer={timer} isRunning={isRunning} />
            <TimeInput />
            <TimerPresets />
            <TimerControls
              toggleTimer={toggleTimer}
              resetTimer={resetTimer}
              isRunning={isRunning}
              timer={timer}
              mode={mode}
              initialTime={initialTime}
            />
            <LapTimes />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimerApp;
