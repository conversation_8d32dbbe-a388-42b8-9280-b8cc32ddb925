import { useState } from 'react';
import { useTimer } from '../contexts/TimerContext';
import { useTheme } from '../contexts/ThemeContext';
import { playSound, SOUND_TYPES, isAudioSupported } from '../utils/soundUtils';
import Button from './Button';

const Settings = () => {
  const { settings, dispatch, actions } = useTimer();
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const handleSettingChange = (key, value) => {
    dispatch({
      type: actions.UPDATE_SETTINGS,
      payload: { [key]: value }
    });
  };

  const testSound = (soundType) => {
    if (settings.soundEnabled) {
      playSound(soundType, 0.5);
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        handleSettingChange('notificationsEnabled', true);
      }
    }
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className={`
          px-3 py-2 rounded-lg transition-all duration-200 flex items-center gap-2
          ${theme.colors.secondary} ${theme.colors.text}
        `}
        ariaLabel="Open settings"
      >
        ⚙️ Settings
      </Button>
    );
  }

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-20" 
        onClick={() => setIsOpen(false)}
      />
      
      {/* Settings Modal */}
      <div className={`
        fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30
        w-full max-w-md mx-4 p-6 rounded-xl
        ${theme.colors.cardBg} ${theme.colors.shadow}
      `}>
        <div className="flex justify-between items-center mb-6">
          <h2 className={`text-xl font-bold ${theme.colors.text}`}>Settings</h2>
          <Button
            onClick={() => setIsOpen(false)}
            className={`
              px-3 py-1 rounded-lg transition-colors
              ${theme.colors.secondary} ${theme.colors.text}
            `}
            ariaLabel="Close settings"
          >
            ✕
          </Button>
        </div>

        <div className="space-y-6">
          {/* Sound Settings */}
          <div>
            <h3 className={`text-lg font-semibold ${theme.colors.text} mb-3`}>
              🔊 Sound
            </h3>
            
            <div className="space-y-3">
              {/* Enable Sound */}
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Enable Sound Alerts
                </label>
                <input
                  type="checkbox"
                  checked={settings.soundEnabled}
                  onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}
                  className="w-4 h-4"
                  disabled={!isAudioSupported()}
                />
              </div>

              {/* Sound Type */}
              {settings.soundEnabled && (
                <div className="flex items-center justify-between">
                  <label className={`${theme.colors.textSecondary}`}>
                    Alert Sound
                  </label>
                  <div className="flex gap-2">
                    <select
                      value={settings.soundType || SOUND_TYPES.BEEP}
                      onChange={(e) => handleSettingChange('soundType', e.target.value)}
                      className={`
                        px-2 py-1 rounded border text-sm
                        ${theme.colors.cardBg} ${theme.colors.text} ${theme.colors.border}
                      `}
                    >
                      <option value={SOUND_TYPES.BEEP}>Beep</option>
                      <option value={SOUND_TYPES.CHIME}>Chime</option>
                      <option value={SOUND_TYPES.BELL}>Bell</option>
                    </select>
                    <Button
                      onClick={() => testSound(settings.soundType || SOUND_TYPES.BEEP)}
                      className={`
                        px-2 py-1 text-xs rounded transition-colors
                        ${theme.colors.primary} text-white
                      `}
                    >
                      Test
                    </Button>
                  </div>
                </div>
              )}

              {/* Tick Sound */}
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Tick Sound While Running
                </label>
                <input
                  type="checkbox"
                  checked={settings.tickSoundEnabled}
                  onChange={(e) => handleSettingChange('tickSoundEnabled', e.target.checked)}
                  className="w-4 h-4"
                  disabled={!settings.soundEnabled}
                />
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div>
            <h3 className={`text-lg font-semibold ${theme.colors.text} mb-3`}>
              🔔 Notifications
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Browser Notifications
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.notificationsEnabled}
                    onChange={(e) => handleSettingChange('notificationsEnabled', e.target.checked)}
                    className="w-4 h-4"
                    disabled={!('Notification' in window) || Notification.permission === 'denied'}
                  />
                  {Notification.permission === 'default' && (
                    <Button
                      onClick={requestNotificationPermission}
                      className={`
                        px-2 py-1 text-xs rounded transition-colors
                        ${theme.colors.primary} text-white
                      `}
                    >
                      Allow
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Display Settings */}
          <div>
            <h3 className={`text-lg font-semibold ${theme.colors.text} mb-3`}>
              🎨 Display
            </h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className={`${theme.colors.textSecondary}`}>
                  Compact Time Display
                </label>
                <input
                  type="checkbox"
                  checked={settings.compactDisplay}
                  onChange={(e) => handleSettingChange('compactDisplay', e.target.checked)}
                  className="w-4 h-4"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <Button
            onClick={() => setIsOpen(false)}
            className={`
              w-full py-2 rounded-lg font-medium transition-colors
              ${theme.colors.primary} text-white
            `}
          >
            Done
          </Button>
        </div>
      </div>
    </>
  );
};

export default Settings;
